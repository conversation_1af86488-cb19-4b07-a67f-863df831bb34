package cn.abcyun.cis.offlinetask.job.dataMigration;

import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.MD5Utils;
import cn.abcyun.cis.core.util.ExecutorUtils;
import cn.abcyun.cis.offlinetask.db.MysqlClient;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.ColumnMapRowMapper;

import java.time.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Phaser;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DataMigrationTask {
    private String clinicId;
    private DataMigrationDbProperties db;
    private LocalDate beginDate;
    private LocalDate endDate;

    public DbMigrationResult migrateData() {
        //迁移最近修改时间在intervalDays天前的sheet,每次迁移有限个数
        Map<String, Object> paramMap = new HashMap<String, Object>() {{
            put("clinicId", clinicId);
            put("intervalDays", db.getIntervalDays());
            put("limit", db.getMaxSheetNumEachProcess());
            put("beginDate", SqlTemplate.YYYYMMDD.format(beginDate));
            put("endDate", SqlTemplate.YYYYMMDD.format(endDate));
        }};

        AtomicInteger taskCnt = new AtomicInteger(0);
        log.info("clinic {} migrateData {} begin", clinicId, beginDate);

        List<CompletableFuture<DataMigrationResultItemOfSingleTask>> futures = new ArrayList<>();

        long beginTime = System.currentTimeMillis();
        DbMigrationResult migrationResult = DbMigrationResult.initDbMigrationResultItem(db);
        migrationResult.getClinicIds().add(clinicId);

        try {
            String lastDelId = null;
            List<String> sheetIds;
            do {
                String querySheetIdsSql = db.getMainQuerySql();
                if (StringUtils.isNotEmpty(lastDelId)) {
                    querySheetIdsSql = querySheetIdsSql + " " + db.getOptionalWhereClause();
                    paramMap.put("lastDelId", lastDelId);
                }
                querySheetIdsSql = querySheetIdsSql + " " + db.getOrderBy();
                //获取sheetIds相关的单据
                long curBeginTime = System.currentTimeMillis();
                sheetIds = DbClientHolder.getOceanBaseClient().getJdbcTemplate().query(querySheetIdsSql, paramMap, (rs, i) -> rs.getString("id"));
                long getSheetCostTime = System.currentTimeMillis() - curBeginTime;
                if (getSheetCostTime > 300) {
                    log.info("clinicId:{}, sheetIds size:{}, cost time:{}ms", clinicId, sheetIds.size(), getSheetCostTime);
                }
                if (CollectionUtils.isEmpty(sheetIds)) {
                    break;
                }
                lastDelId = sheetIds.get(sheetIds.size() - 1);
                migrationResult.getSheetCount().getAndAdd(sheetIds.size());

                //去掉最近修改时间在endTime之外的
                List<String> finalSheetIds = sheetIds;
                if (CollectionUtils.isNotEmpty(finalSheetIds)) {
                    futures.add(ExecutorUtils.futureSupplyAsync(() -> doMigrateData(finalSheetIds, null, null), "sub"));
                }

                if (taskCnt.getAndIncrement() % 100 == 0) {
                    log.info("clinicId {}, taskCnt: {}.", clinicId, taskCnt.get());
                }

            } while (CollectionUtils.isNotEmpty(sheetIds) && sheetIds.size() == db.getMaxSheetNumEachProcess());
        } catch (Exception e) {
            log.error("{} data migration error.", clinicId, e);
            migrationResult.getFailedInfos().add(clinicId + ": " + e.getMessage());
        }

        for (CompletableFuture<DataMigrationResultItemOfSingleTask> resultItemFuture : futures) {
            DataMigrationResultItemOfSingleTask singleTaskResult = resultItemFuture.exceptionally(throwable -> {
                log.error("future {} data migration error.", clinicId, throwable);
                migrationResult.getFailedInfos().add(clinicId + ": " + throwable.getMessage().substring(0, Math.min(150, throwable.getMessage().length())));
                return null;
            }).join();

            if (singleTaskResult != null && singleTaskResult.getResultItemMap() != null) {
                DbMigrationResult.mergeResult(migrationResult, singleTaskResult.getResultItemMap());
            }
        }
        migrationResult.getCostTime().set((System.currentTimeMillis() - beginTime) / 1000);

        log.info("clinic {} migrateData {} finished. cost: {}s", clinicId, beginDate, migrationResult.getCostTime());
        return migrationResult;
    }

    private List<String> removeUnSelectSheetIds(List<String> sheetIds, Set<String> relatedSheetIds) {
        if (db.getMode() != DataMigrationJob.Mode.CLEAN_NO_RELATED_HISTORY) {
            return sheetIds;
        }

        if (CollectionUtils.isEmpty(relatedSheetIds) || CollectionUtils.isEmpty(sheetIds)) {
            return sheetIds;
        }

        if (StringUtils.isEmpty(db.getRelatedQuerySql())) {
            return new ArrayList<>(relatedSheetIds);
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("ids", relatedSheetIds);
        List<String> existRelatedSheetIds = DbClientHolder.getOceanBaseClient().getJdbcTemplate().query(db.getRelatedQuerySql(), paramMap, (rs, i) -> rs.getString("id"));
        if (CollectionUtils.isNotEmpty(existRelatedSheetIds)) {
            return relatedSheetIds.stream().filter(id -> !existRelatedSheetIds.contains(id)).collect(Collectors.toList());
        }
        return new ArrayList<>(relatedSheetIds);
    }

    private DataMigrationResultItemOfSingleTask doMigrateData(List<String> sheetIds, String curBeginDate, String curDndDate) {
        if (CollectionUtils.isEmpty(sheetIds) || CollectionUtils.isEmpty(db.getTables())) {
            return null;
        }

        log.debug("sub submit sheetIdSize {}, first sheetId: {}", sheetIds.size(), sheetIds.get(0));
        long startTime = System.currentTimeMillis();
        MysqlClient hotDbClient = DbClientHolder.getHotDbClientMap().get(db.getName());
        MysqlClient coldDbClient = DbClientHolder.getColdDbClientMap().get(db.getName());

        DataMigrationResultItemOfSingleTask singleTaskResult = new DataMigrationResultItemOfSingleTask();
        Map<String, DataMigrationResultItem> resultItemMap = singleTaskResult.getResultItemMap();

        for (DataMigrationTableProperties table : db.getTables()) {
            long beginTime = System.currentTimeMillis();

            DataMigrationResultItem resultItem = new DataMigrationResultItem();
            resultItem.setTableName(table.getName());

            Map<String, Object> paramMap = new HashMap<String, Object>() {{
                put("indexIds", sheetIds);
                put("clinicId", clinicId);
            }};

            //保证迁移过程中的事务操作
            //1. 批量热库和冷库的数据 对比 决定是否更新 selectBySheetIds
            String queryDataSql = SqlTemplate.formatSql(SqlTemplate.BATCH_QUERY_BY_IDS_SQL, table.getName(), table.getIndexKey());
            Map<String, Map<String, Object>> hotDataMaps = getDataMap(hotDbClient, queryDataSql, paramMap);
            if (MapUtils.isEmpty(hotDataMaps)) {
                continue;
            }

            long getHotData = System.currentTimeMillis();
            long getHotDataCost = getHotData - beginTime;
            log.debug("getHotData cost {}, size {}", getHotDataCost, hotDataMaps.size());

            Map<String, Map<String, Object>> coldDataMaps = getDataMap(coldDbClient, queryDataSql, paramMap);

            long getColdData = System.currentTimeMillis();
            long getColdDataCost = getColdData - getHotData;
            log.debug("getColdData cost {}, size {}", getColdDataCost, coldDataMaps.size());

            resultItem.getCount().getAndAdd(hotDataMaps.size());
            //2. 插入更新cold-db insertOnDuplicateKey
            List<Map<String, Object>> needUpdateMaps = hotDataMaps.entrySet().stream().filter(entry -> {
                String id = entry.getKey();
                Map<String, Object> hotDataMap = entry.getValue();
                Map<String, Object> coldDataMap = coldDataMaps.get(id);
                return StringUtils.isNotEmpty(id) && hotDataMap != null && compareData(hotDataMap, coldDataMap) == 1;
            }).map(Map.Entry::getValue).collect(Collectors.toList());
            long compareCost = System.currentTimeMillis() - getColdData;
            log.debug("compareData cost {}", compareCost);

            int differentCount = needUpdateMaps.size();
            resultItem.getDifferentCount().getAndAdd(differentCount);

            int executedCount = 0;
            if (db.isEnableSync()) {
                executedCount = insertDataToCold(coldDbClient, needUpdateMaps, table.getName());
                resultItem.getUpdatedCount().getAndAdd(executedCount);
                long cost = System.currentTimeMillis() - getColdData;
                log.debug("insertOnDuplicateKey cost {}, executedCount :{}", cost, executedCount);
            }

            if (differentCount > 0) {
                List<String> needUpdateIds = needUpdateMaps.stream().map(map -> map.get("id").toString()).collect(Collectors.toList());
                log.warn("table: {}, differentCount: {}, updateCount: {} updateIds {}", table.getName(), differentCount, executedCount, needUpdateIds);
            }

            //3. 删除热库数据hot-db deleteByIds
            long delCost = 0;
            if (db.isEnableSync() && db.isEnableDel()) {
                long delBeginTime = System.currentTimeMillis();
                List<String> ids = new ArrayList<>(hotDataMaps.keySet());
                int batchSize = 200;
                for (int i = 0; i < ids.size(); i += batchSize) {
                    int toIndex = Math.min(i + batchSize, ids.size());
                    List<String> batchIds = ids.subList(i, toIndex);
                    int delCount = batchDeleteHotData(hotDbClient, batchIds, table.getName());
                    resultItem.getDeletedCount().getAndAdd(delCount);
                }
                delCost = System.currentTimeMillis() - delBeginTime;
            }

            long cost = (System.currentTimeMillis() - beginTime);
            if (cost >= 4500) {
                if (db.getMode() != DataMigrationJob.Mode.NORMAL) {
                    log.info("beginDate {}, endDate {}, process table {} result, cost {}ms, getHotDataCost {}ms，getColdDatCost {}ms, compareCost {}ms,  delCost {}ms, resultItem {}", curBeginDate, curDndDate, table.getName(), cost, getHotDataCost, getColdDataCost, compareCost, delCost, JsonUtils.dump(resultItem));
                } else {
                    log.info("clinic {}, process table {} result, cost {}ms, getHotDataCost {}ms，getColdDatCost {}ms, compareCost {}ms,  delCost {}ms, resultItem {}", clinicId, table.getName(), cost, getHotDataCost, getColdDataCost, compareCost, delCost, JsonUtils.dump(resultItem));
                }

            }
            resultItemMap.put(table.getName(), resultItem);
        }

        long allCost = (System.currentTimeMillis() - startTime);
        singleTaskResult.setCostTime(allCost);
        singleTaskResult.setBeginDate(curBeginDate);
        singleTaskResult.setEndDate(curDndDate);
        singleTaskResult.setResultItemMap(resultItemMap);
        return singleTaskResult;
    }

    private int batchDeleteHotData(MysqlClient mysqlClient, List<String> ids, String tableName) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }

        Map<String, Object> paramMap = new HashMap<String, Object>() {{
            put("indexIds", ids);
            put("clinicId", clinicId);
        }};

        String delSql = SqlTemplate.formatSql(SqlTemplate.BATCH_DELETE_BY_IDS_SQL, tableName, "id");
        return mysqlClient.executeSqlTimeOut(delSql, paramMap);
    }

    public Map<String, Map<String, Object>> getDataMap(MysqlClient mysqlClient, String sql, Map<String, Object> paramMap) {
        List<Map<String, Object>> result = mysqlClient.getJdbcTemplate().query(sql, paramMap, new ColumnMapRowMapper());
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }

        return result.stream().collect(Collectors.toMap(dataMap -> dataMap.get("id").toString(), Function.identity(), (a, b) -> a));
    }

    private int compareData(Map<String, Object> hotDataMap, Map<String, Object> coldDataMap) {
        if (MapUtils.isEmpty(hotDataMap)) {
            return 0;
        }

        // Fast path: check if maps have different sizes or coldDataMap is null
        if (coldDataMap == null || hotDataMap.size() != coldDataMap.size()) {
            return 1;
        }

        // Compare the string representation of each value
        for (String key : hotDataMap.keySet()) {
            Object hotValue = hotDataMap.get(key);
            Object coldValue = coldDataMap.get(key);

            // Convert values to string representation (same as in signMap)
            String hotValueStr = (hotValue != null) ? hotValue.toString() : null;
            String coldValueStr = (coldValue != null) ? coldValue.toString() : null;

            if (!Objects.equals(hotValueStr, coldValueStr)) {
                return 1;
            }
        }

        // Maps are identical
        return 0;
    }

    public static String signMap(Map<String, Object> keyData) {
        if (keyData == null || keyData.size() == 0) {
            return "";
        }

        String data = keyData.keySet().stream().sorted().map(key -> {
            Object o = keyData.get(key);
            String v = null;
            if (o != null) {
                v = o.toString();
            }
            return String.format("%s:%s", key, v);
        }).collect(Collectors.joining(","));

        return MD5Utils.sign(data);
    }

    private int insertDataToCold(MysqlClient mysqlClient, List<Map<String, Object>> hotDataMaps, String tableName) {
        if (CollectionUtils.isEmpty(hotDataMaps)) {
            return 0;
        }
        Map<String, Object> hotDataMap = hotDataMaps.get(0);
        //获取所有的字段list
        List<String> columns = new ArrayList<>(hotDataMap.keySet());
        //拼接写入字段
        String columnsStr = columns.stream().map(column -> "`" + column + "`").collect(Collectors.joining(","));

        //拼接写入字段的value
        String valuesStr = columns.stream().map(column -> ":" + column).collect(Collectors.joining(","));

        //拼接写入字段的duplicate value
        String duplicateKeyValuesStr = columns.stream().filter(column -> !Objects.equals(column, "id")).map(column -> String.format("`%s` = values(`%s`)", column, column)).collect(Collectors.joining(","));

        String insertUpdateSql = SqlTemplate.formatSql(SqlTemplate.INSERT_RECORD_SQL_TEMPLATE, tableName);
        return Arrays.stream(mysqlClient.getJdbcTemplate().batchUpdate(String.format(insertUpdateSql, columnsStr, valuesStr, duplicateKeyValuesStr), hotDataMaps.toArray(new Map[0]))).sum();
    }

    public DbMigrationResult process() {
        Map<String, Object> paramMap = new HashMap<String, Object>() {{
            put("limit", db.getMaxSheetNumEachProcess());
        }};


        // db.getBeginDateTime db.getEndDateTime 按天处理 每天每次最多查n条数据
        long beginTime = System.currentTimeMillis();
        DbMigrationResult migrationResult = DbMigrationResult.initDbMigrationResultItem(db);
        Phaser phaser2 = new Phaser(1);
        try {
            LocalDate curDate = this.beginDate;
            while (curDate.isBefore(this.endDate)) {
                LocalDate curEndDate = curDate.plusDays(1);
                curEndDate = curEndDate.isBefore(this.endDate) ? curEndDate : this.endDate;
                paramMap.put("beginDate", curDate.format(SqlTemplate.YYYYMMDD));
                paramMap.put("endDate", curEndDate.format(SqlTemplate.YYYYMMDD));

                log.info("main cleanHistoryTask scan.  beginDate: {}, endDate: {}", this.beginDate, this.endDate);

                try {
                    doCleanHistoryTask(paramMap, phaser2, migrationResult);
                } catch (Exception e) {
                    log.error("beginDate: {}, endDate: {}, doCleanHistoryTask error.", curDate, curEndDate, e);
                    migrationResult.getFailedInfos().add(beginDate + " " + endDate + " doCleanHistoryTask  failed."
                            + e.getMessage().substring(0, Math.min(e.getMessage().length(), 100)));
                }
                curDate = curEndDate;
            }
        } catch (Exception e) {
            log.error("beginDate: {}, endDate: {}  cleanHistoryTask error.", beginDate, endDate);
            migrationResult.getFailedInfos().add(beginDate + " " + endDate + "cleanHistoryTask  failed.");
        }
        phaser2.arriveAndAwaitAdvance();
        migrationResult.getCostTime().set((System.currentTimeMillis() - beginTime));
        return migrationResult;
    }

    private void doCleanHistoryTask(Map<String, Object> paramMap, Phaser phaser2, DbMigrationResult migrationResult) {
        String beginDateTask = (String) paramMap.get("beginDate");
        String endDateTask = (String) paramMap.get("endDate");
        AtomicInteger taskCnt = new AtomicInteger(0);
        String lastDelId = null;
        List<String> mainIds;
        //用于判断关联删除的场景
        Set<String> relatedSheetIds = null;
        long beginTime = System.currentTimeMillis();
        do {
            String querySheetIdsSql = db.getMainQuerySql();
            if (StringUtils.isNotEmpty(lastDelId)) {
                if (StringUtils.isNotEmpty(db.getOptionalMainQuerySql())) {
                    querySheetIdsSql = db.getOptionalMainQuerySql();
                } else {
                    querySheetIdsSql = querySheetIdsSql + " " + db.getOptionalWhereClause();
                }
                paramMap.put("lastDelId", lastDelId);
            }
            querySheetIdsSql = querySheetIdsSql + " " + db.getOrderBy();
            //获取sheetIds相关的单据
            long curBeginTime = System.currentTimeMillis();
            if (db.getMode() == DataMigrationJob.Mode.CLEAN_NO_RELATED_HISTORY) {
                List<Map<String, Object>> sheetResult = DbClientHolder.getOceanBaseClient().getJdbcTemplate().query(querySheetIdsSql, paramMap, new ColumnMapRowMapper());
                mainIds = new ArrayList<>();
                relatedSheetIds = new HashSet<>();
                for (Map<String, Object> stringObjectMap : sheetResult) {
                    if (stringObjectMap == null) {
                        continue;
                    }

                    String id = (String) stringObjectMap.get("id");
                    if (StringUtils.isNotEmpty(id)) {
                        mainIds.add(id);
                    }

                    String relatedSheetId = (String) stringObjectMap.get("related_sheet_id");
                    if (StringUtils.isNotEmpty(relatedSheetId)) {
                        relatedSheetIds.add(relatedSheetId);
                    }
                }
            } else {
                mainIds = DbClientHolder.getOceanBaseClient().getJdbcTemplate().query(querySheetIdsSql, paramMap, (rs, i) -> rs.getString("id"));
            }
            long getSheetCostTime = System.currentTimeMillis() - curBeginTime;
            if (getSheetCostTime > 500) {
                log.info("sheetIds size:{}, cost time:{}ms", mainIds.size(), getSheetCostTime);
            }
            if (CollectionUtils.isEmpty(mainIds)) {
                break;
            }
            lastDelId = mainIds.get(mainIds.size() - 1);
            migrationResult.getSheetCount().getAndAdd(mainIds.size());

            //去掉最近修改时间在endTime之外的
            List<String> finalSheetIds = removeUnSelectSheetIds(mainIds, relatedSheetIds);
            if (CollectionUtils.isEmpty(finalSheetIds)) {
                continue;
            }

            phaser2.register();
            try {
                //提交具体的数据迁移任务
                ExecutorUtils.futureSupplyAsync(() -> doMigrateData(finalSheetIds, beginDateTask, endDateTask), "sub")
                        .whenComplete((singleTaskResult, throwable) -> {
                            if (taskCnt.get() % 100 == 0) {
                                log.info("sub {}, taskCnt: {} finished.", beginDateTask, taskCnt.get());
                            }

                            try {
                                if (throwable != null) {
                                    log.error("beginDate: {}, endDate: {} doCleanHistoryTask error.", beginDateTask, endDateTask, throwable);
                                    if (StringUtils.isNotEmpty(throwable.getMessage())) {
                                        migrationResult.getFailedInfos().add(throwable.getMessage().substring(0, Math.min(throwable.getMessage().length(), 100)));
                                    }

                                    if (StringUtils.isNotEmpty(clinicId)) {
                                        migrationResult.getFailedClinicIds().add(clinicId);
                                    }
                                }

                                if (singleTaskResult != null) {
                                    DbMigrationResult.mergeResult(migrationResult, singleTaskResult.getResultItemMap());
                                    taskCnt.incrementAndGet();
                                    if (singleTaskResult.getCostTime() > 5000) {
                                        log.info("sub beginDate: {}, endDate: {}, taskCnt: {}, resultItem {}, cost {}", beginDateTask, endDateTask, taskCnt.get(), migrationResult, singleTaskResult.getCostTime());
                                    }
                                }
                            } catch (Exception e) {
                                log.error("process result failed. ", e);
                                migrationResult.getFailedInfos().add("beginDate " + beginDateTask + " endDate " + endDateTask + "doMigrateData error.");
                            } finally {
                                phaser2.arriveAndDeregister();
                            }
                        });
            } catch (Throwable e) {
                log.error("submit task failed. ", e);
                phaser2.arriveAndDeregister();
            }

        } while (CollectionUtils.isNotEmpty(mainIds) && mainIds.size() == db.getMaxSheetNumEachProcess() && taskCnt.get() < 10000000);

        log.info("main scan beginDate: {} endDate: {} finished. cost: {}, sheetTotalCount: {}", paramMap.get("beginDate"), paramMap.get("endDate"), migrationResult.getSheetCount(), (System.currentTimeMillis() - beginTime) / 1000);
    }
}
